{"name": "ai-ocr", "productName": "ai-ocr", "version": "1.0.0", "description": "AI OCR application for text recognition", "main": ".vite/build/main.js", "scripts": {"start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "lint": "eslint --ext .ts,.tsx ."}, "keywords": [], "author": {"name": "jf", "email": "https://gitlab.hunantianxing.com/"}, "license": "MIT", "devDependencies": {"@electron-forge/cli": "^7.8.2", "@electron-forge/maker-deb": "^7.8.2", "@electron-forge/maker-rpm": "^7.8.2", "@electron-forge/maker-squirrel": "^7.8.2", "@electron-forge/maker-zip": "^7.8.2", "@electron-forge/plugin-auto-unpack-natives": "^7.8.2", "@electron-forge/plugin-fuses": "^7.8.2", "@electron-forge/plugin-vite": "^7.8.2", "@electron/fuses": "^1.8.0", "@types/electron-squirrel-startup": "^1.0.2", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitejs/plugin-react": "^4.6.0", "electron": "37.2.4", "eslint": "^8.57.1", "eslint-plugin-import": "^2.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "typescript": "~4.5.4", "vite": "^5.4.19"}, "dependencies": {"electron-squirrel-startup": "^1.0.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.7.1"}}