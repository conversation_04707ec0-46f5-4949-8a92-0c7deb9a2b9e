/* 图片显示区域 */
.image-display-section {
  background-color: #f8fafc;
  border-radius: 16px;
  padding: 32px;
  border: 2px dashed #cbd5e1;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 24px;
  text-align: center;
}

.image-placeholder {
  text-align: center;
  padding: 48px 24px;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.placeholder-text {
  font-size: 16px;
  color: #64748b;
  margin-bottom: 8px;
  font-weight: 500;
}

.placeholder-hint {
  font-size: 14px;
  color: #94a3b8;
}

/* 选中图片显示区域 */
.selected-image-content {
  text-align: center;
}

.selected-image-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.clear-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.clear-btn:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

.selected-image-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.selected-image {
  max-width: 100%;
  max-height: 400px;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid #e2e8f0;
}

.selected-image-info {
  text-align: center;
  color: #64748b;
  font-size: 14px;
  background: #ffffff;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.selected-image-info p {
  margin: 0;
} 