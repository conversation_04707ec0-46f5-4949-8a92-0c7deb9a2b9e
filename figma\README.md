# Fast OCR UI原型

这是一个使用HTML + CSS + JavaScript实现的Fast OCR应用UI原型，符合Figma设计规范。

## 功能特性

### 🎨 设计特点
- 现代化渐变设计风格
- 响应式布局，支持移动端
- 流畅的动画效果和交互反馈
- 符合Figma设计规范的UI组件

### 📱 页面结构
1. **左侧菜单栏**
   - 应用Logo和品牌标识
   - 导航菜单（首页、设置）
   - 悬停和选中状态效果

2. **首页**
   - 截图识别按钮（带渐变效果）
   - 图片显示区域（占位符状态）
   - 清晰的操作指引

3. **设置页面**
   - OpenAI API配置表单
   - API地址、密钥、模型选择
   - 保存和重置按钮

## 文件结构

```
figma/
├── index.html          # 主HTML文件
├── styles.css          # 样式文件
├── script.js           # 交互脚本
└── README.md          # 说明文档
```

## 使用方法

1. 直接在浏览器中打开 `index.html` 文件
2. 点击左侧菜单切换页面
3. 体验各种交互效果

## 技术实现

### HTML结构
- 语义化标签，良好的可访问性
- 清晰的组件层次结构
- 符合现代Web标准

### CSS样式
- Flexbox布局系统
- CSS Grid辅助布局
- 渐变背景和阴影效果
- 响应式媒体查询
- 平滑的过渡动画

### JavaScript交互
- 页面切换功能
- 按钮点击反馈
- 表单焦点效果
- 无依赖的纯原生实现

## 设计规范

### 颜色方案
- 主色调：蓝紫色渐变 (#667eea → #764ba2)
- 背景色：浅灰色 (#f8fafc)
- 文字色：深灰色 (#1e293b)
- 边框色：中灰色 (#e2e8f0)

### 字体系统
- 系统字体栈，确保跨平台一致性
- 清晰的字体层次结构
- 适当的行高和间距

### 间距规范
- 使用8px的倍数作为间距基准
- 组件内边距：16px-32px
- 组件间距：24px-48px

## 浏览器兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 后续开发建议

1. **功能集成**：将UI原型与实际的截图和OCR功能集成
2. **状态管理**：添加表单验证和数据持久化
3. **主题系统**：实现深色模式和多主题支持
4. **国际化**：添加多语言支持
5. **无障碍优化**：增强键盘导航和屏幕阅读器支持

## 许可证

MIT License 