/* 侧边栏 */
.sidebar {
  width: 200px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  transition: width 0.3s ease;
  position: relative;
}

.sidebar.collapsed {
  width: 80px;
}

/* 侧边栏头部 */
.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 菜单底部切换按钮容器 */
.menu-toggle-container {
  margin-top: auto;
  padding: 16px 24px;
  display: flex;
  justify-content: center;
}

/* 侧边栏切换按钮 */
.sidebar-toggle {
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid #fbbf24;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 9px;
}

.sidebar-toggle:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.08);
  border-color: #f59e0b;
}

.toggle-icon {
  transition: transform 0.3s ease;
  font-size: 9px;
}

.sidebar.collapsed .toggle-icon {
  transform: rotate(180deg);
}

/* Logo */
.logo {
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.logo-icon {
  font-size: 24px;
  margin-right: 12px;
  transition: margin 0.3s ease;
}

.logo-text {
  font-size: 20px;
  font-weight: 600;
  letter-spacing: -0.5px;
  transition: opacity 0.3s ease;
  white-space: nowrap;
  overflow: hidden;
}

.sidebar.collapsed .logo-icon {
  margin-right: 0;
}

.sidebar.collapsed .logo-text {
  opacity: 0;
  width: 0;
}

/* 菜单 */
.menu {
  flex: 1;
  padding: 16px 0;
  display: flex;
  flex-direction: column;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 4px 12px;
  border-radius: 12px;
  position: relative;
}

.menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateX(4px);
}

.menu-item.active {
  background-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.menu-icon {
  font-size: 18px;
  margin-right: 12px;
  width: 24px;
  text-align: center;
  transition: margin 0.3s ease;
}

.menu-text {
  font-weight: 500;
  font-size: 14px;
  transition: opacity 0.3s ease;
  white-space: nowrap;
  overflow: hidden;
}

.sidebar.collapsed .menu-item {
  padding: 16px;
  justify-content: center;
}

.sidebar.collapsed .menu-icon {
  margin-right: 0;
}

.sidebar.collapsed .menu-text {
  opacity: 0;
  width: 0;
}

.sidebar.collapsed .menu-toggle-container {
  padding: 16px;
} 