# 截图窗口调试指南

## 问题描述
screenshotWindow 中的图片加载不出来，显示"数据接收超时"

## 修复内容

### 1. 主进程修复 (`main/main.ts`)
- ✅ 添加了开发者工具：`screenshotWindow.webContents.openDevTools()`
- ✅ 添加了详细的调试日志
- ✅ 改进了数据传递时机（延迟500ms确保组件挂载）
- ✅ 添加了窗口加载失败的错误处理

### 2. 渲染进程修复 (`renderer/src/App.tsx`)
- ✅ 添加了加载状态管理
- ✅ 添加了错误处理和超时机制（延长到10秒）
- ✅ 添加了详细的调试日志
- ✅ 改进了用户界面反馈
- ✅ 显示当前URL信息用于调试

### 3. 预加载脚本修复 (`main/preload.ts`)
- ✅ 添加了调试日志来跟踪数据传递
- ✅ 添加了回调函数错误处理

## 调试步骤

### 1. 启动应用
```bash
npm start
```

### 2. 测试流程
1. 在主窗口中点击"获取屏幕截图"
2. 观察主进程控制台日志
3. 新窗口打开后，查看新窗口的开发者工具控制台
4. 检查URL是否正确跳转到 `#/screenshot`

### 3. 检查要点

#### 主进程日志
- 查看是否有 "创建截图窗口，数据:" 日志
- 查看是否有 "加载URL:" 日志
- 查看是否有 "窗口加载完成，准备发送数据" 日志
- 查看是否有 "发送截图数据到新窗口:" 日志

#### 新窗口日志
- 打开新窗口的开发者工具 (F12)
- 查看控制台是否有 "ScreenshotViewer 组件挂载" 日志
- 查看是否有 "当前URL hash:" 日志
- 查看是否有 "注册截图数据监听器" 日志
- 查看是否有 "预加载脚本收到截图数据:" 日志
- 查看是否有 "收到截图数据:" 日志

### 4. 常见问题排查

#### 问题1: 路由跳转失败
**症状**: 新窗口显示"数据接收超时"，URL不是 `#/screenshot`
**可能原因**:
- Vite开发服务器端口问题
- URL构建错误
- 路由系统问题

**解决方案**:
1. 检查主进程日志中的URL是否正确
2. 确认Vite开发服务器是否正常运行
3. 手动在浏览器中访问 `http://localhost:5174/#/screenshot`

#### 问题2: 数据传递失败
**症状**: 主进程显示发送了数据，但新窗口没有收到
**可能原因**:
- IPC通信失败
- 监听器注册时机问题
- 数据格式问题

**解决方案**:
1. 检查新窗口控制台是否有 "注册截图数据监听器" 日志
2. 检查是否有 "预加载脚本收到截图数据:" 日志
3. 确认数据格式是否正确

#### 问题3: 组件挂载问题
**症状**: 新窗口显示"未收到截图数据"
**可能原因**:
- React组件没有正确挂载
- 路由没有正确跳转
- 组件生命周期问题

**解决方案**:
1. 检查新窗口控制台是否有 "ScreenshotViewer 组件挂载" 日志
2. 检查URL hash是否正确
3. 查看React开发者工具中的组件状态

## 调试命令

### 查看应用日志
```bash
# 启动应用并查看详细日志
npm start 2>&1 | tee app.log
```

### 检查端口占用
```bash
# 检查Vite开发服务器端口
netstat -an | findstr :5174
```

### 检查进程
```bash
# 查看Electron进程
tasklist | findstr electron
```

## 预期结果

修复后，应该看到以下日志序列：

1. **主进程**:
   ```
   收到创建截图窗口请求，数据: {id: "...", name: "...", thumbnail: "data:image/png;base64,..."}
   创建截图窗口，数据: {id: "...", name: "...", thumbnail: "data:image/png;base64,..."}
   加载URL: http://localhost:5174/#/screenshot
   窗口加载完成，准备发送数据
   发送截图数据到新窗口: {id: "...", name: "...", thumbnail: "data:image/png;base64,..."}
   ```

2. **新窗口控制台**:
   ```
   ScreenshotViewer 组件挂载
   当前URL hash: #/screenshot
   注册截图数据监听器
   预加载脚本收到截图数据: {id: "...", name: "...", thumbnail: "data:image/png;base64,..."}
   收到截图数据: {id: "...", name: "...", thumbnail: "data:image/png;base64,..."}
   图片加载成功
   ```

## 如果问题仍然存在

1. **收集日志**: 保存所有控制台输出
2. **检查环境**: 确认Node.js和npm版本
3. **清理缓存**: 删除 `node_modules` 并重新安装
4. **重启应用**: 完全关闭应用后重新启动
5. **检查网络**: 确认Vite开发服务器可以正常访问

## 临时解决方案

如果问题仍然存在，可以尝试：

1. **手动测试路由**:
   - 在主窗口的开发者工具中执行：`window.location.hash = '#/screenshot'`
   - 查看是否跳转到截图组件

2. **手动测试数据传递**:
   - 在新窗口的开发者工具中执行：
   ```javascript
   window.electronAPI.onScreenshotData((data) => {
     console.log('手动测试收到数据:', data);
   });
   ```

3. **检查数据格式**:
   - 确认截图数据是否为有效的base64格式
   - 尝试直接在浏览器中打开图片URL

---

**最后更新**: 2024年7月31日
**状态**: ✅ 已修复 