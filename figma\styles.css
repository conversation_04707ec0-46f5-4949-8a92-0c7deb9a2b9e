/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    background-color: #f8fafc;
    color: #1e293b;
    line-height: 1.6;
}

/* 应用容器 */
.app-container {
    display: flex;
    height: 100vh;
    overflow: hidden;
}

/* 侧边栏 */
.sidebar {
    width: 240px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    transition: width 0.3s ease;
    position: relative;
}

.sidebar.collapsed {
    width: 80px;
}

/* 侧边栏头部 */
.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 侧边栏切换按钮 */
.sidebar-toggle {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 12px;
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.toggle-icon {
    transition: transform 0.3s ease;
}

.sidebar.collapsed .toggle-icon {
    transform: rotate(180deg);
}

/* Logo */
.logo {
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.logo-icon {
    font-size: 24px;
    margin-right: 12px;
    transition: margin 0.3s ease;
}

.logo-text {
    font-size: 20px;
    font-weight: 600;
    letter-spacing: -0.5px;
    transition: opacity 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
}

.sidebar.collapsed .logo-icon {
    margin-right: 0;
}

.sidebar.collapsed .logo-text {
    opacity: 0;
    width: 0;
}

/* 菜单 */
.menu {
    flex: 1;
    padding: 16px 0;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 16px 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 4px 12px;
    border-radius: 12px;
    position: relative;
}

.menu-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateX(4px);
}

.menu-item.active {
    background-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.menu-icon {
    font-size: 18px;
    margin-right: 12px;
    width: 24px;
    text-align: center;
    transition: margin 0.3s ease;
}

.menu-text {
    font-weight: 500;
    font-size: 14px;
    transition: opacity 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
}

.sidebar.collapsed .menu-item {
    padding: 16px;
    justify-content: center;
}

.sidebar.collapsed .menu-icon {
    margin-right: 0;
}

.sidebar.collapsed .menu-text {
    opacity: 0;
    width: 0;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    overflow-y: auto;
    background-color: #ffffff;
}

/* 页面 */
.page {
    display: none;
    padding: 32px;
    min-height: 100%;
}

.page.active {
    display: block;
}

/* 页面头部 */
.page-header {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid #e2e8f0;
}

.page-header h1 {
    font-size: 28px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 8px;
}

.page-header p {
    color: #64748b;
    font-size: 16px;
}

/* 内容区域 */
.content-area {
    max-width: 800px;
}

/* 截图区域 */
.screenshot-section {
    margin-bottom: 48px;
    text-align: center;
}

.screenshot-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 20px 32px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    margin-bottom: 16px;
}

.screenshot-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.button-icon {
    font-size: 24px;
    margin-right: 12px;
}

.button-text {
    font-size: 18px;
    font-weight: 600;
}

.button-description {
    color: #64748b;
    font-size: 14px;
    max-width: 300px;
    margin: 0 auto;
}

/* 图片显示区域 */
.image-display-section {
    background-color: #f8fafc;
    border-radius: 16px;
    padding: 32px;
    border: 2px dashed #cbd5e1;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 24px;
    text-align: center;
}

.image-placeholder {
    text-align: center;
    padding: 48px 24px;
}

.placeholder-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.6;
}

.placeholder-text {
    font-size: 16px;
    color: #64748b;
    margin-bottom: 8px;
    font-weight: 500;
}

.placeholder-hint {
    font-size: 14px;
    color: #94a3b8;
}

/* 设置表单 */
.settings-form {
    background-color: #ffffff;
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
}

.form-group {
    margin-bottom: 24px;
}

.form-label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
}

.form-input,
.form-select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background-color: #ffffff;
}

.form-input:focus,
.form-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input::placeholder {
    color: #9ca3af;
}

/* 按钮 */
.form-actions {
    display: flex;
    gap: 12px;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e5e7eb;
}

.btn {
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    outline: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background-color: #e5e7eb;
    border-color: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .app-container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
        padding: 0;
    }
    
    .sidebar.collapsed {
        width: 100%;
    }
    
    .sidebar-header {
        padding: 16px;
    }
    
    .menu {
        display: flex;
        justify-content: center;
        gap: 16px;
        padding: 16px 0;
    }
    
    .menu-item {
        flex-direction: column;
        text-align: center;
        padding: 12px 16px;
    }
    
    .menu-icon {
        margin-right: 0;
        margin-bottom: 4px;
    }
    
    .sidebar.collapsed .menu-item {
        padding: 12px 16px;
    }
    
    .sidebar.collapsed .menu-text {
        opacity: 1;
        width: auto;
    }
    
    .page {
        padding: 16px;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.page.active {
    animation: fadeIn 0.3s ease-out;
}

/* 滚动条样式 */
.main-content::-webkit-scrollbar {
    width: 6px;
}

.main-content::-webkit-scrollbar-track {
    background: #f1f5f9;
}

.main-content::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
} 