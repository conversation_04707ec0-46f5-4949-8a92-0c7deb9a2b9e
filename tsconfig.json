{"compilerOptions": {"target": "ESNext", "module": "commonjs", "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "noImplicitAny": true, "sourceMap": true, "baseUrl": ".", "outDir": "dist", "moduleResolution": "node", "resolveJsonModule": true, "strict": false, "noEmit": true, "isolatedModules": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true}, "include": ["main/**/*", "renderer/**/*"], "exclude": ["node_modules"]}