/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    height: auto;
    padding: 0;
  }
  
  .sidebar.collapsed {
    width: 100%;
  }
  
  .sidebar-header {
    padding: 16px;
  }
  
  .menu {
    display: flex;
    justify-content: center;
    gap: 16px;
    padding: 16px 0;
  }
  
  .menu-item {
    flex-direction: column;
    text-align: center;
    padding: 12px 16px;
  }
  
  .menu-icon {
    margin-right: 0;
    margin-bottom: 4px;
  }
  
  .sidebar.collapsed .menu-item {
    padding: 12px 16px;
  }
  
  .sidebar.collapsed .menu-text {
    opacity: 1;
    width: auto;
  }
  
  .menu-toggle-container {
    margin-top: 16px;
    padding: 0;
  }
  
  .page {
    padding: 16px;
  }
  
  .screenshot-button {
    flex-direction: column;
    gap: 8px;
  }
} 