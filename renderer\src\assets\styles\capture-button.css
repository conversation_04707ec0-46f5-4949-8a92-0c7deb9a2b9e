/* 截图区域 */
.screenshot-section {
  margin-bottom: 48px;
  text-align: center;
}

.screenshot-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 20px 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  margin-bottom: 16px;
  flex-direction: column;
  gap: 8px;
}

.screenshot-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.button-icon {
  font-size: 24px;
}

.button-text {
  font-size: 18px;
  font-weight: 600;
}

.button-description {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  max-width: 300px;
  margin: 0 auto;
} 